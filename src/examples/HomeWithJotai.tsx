/**
 * 在 Home 页面中使用 Jotai 的示例
 * 展示如何将现有的 useState 迁移到 Jotai
 */

import React, { useCallback, memo } from 'react';
import { useAtom } from 'jotai';
import GeneralInput from '@/components/GeneralInput';
import Slogan from '@/components/Slogan';
import ChatView from '@/components/ChatView';
import { productList, defaultProduct } from '@/utils/constants';
import { inputInfoAtom, currentProductAtom } from '@/store';

type HomeProps = Record<string, never>;

const HomeWithJotai: React.FC<HomeProps> = memo(() => {
  // 使用 Jotai 原子状态替代 useState
  const [inputInfo, setInputInfo] = useAtom(inputInfoAtom);
  const [product, setProduct] = useAtom(currentProductAtom);

  // 使用默认产品，如果没有选择的话
  const currentProduct = product || defaultProduct;

  const changeInputInfo = useCallback(
    (info: CHAT.TInputInfo) => {
      setInputInfo(info);
    },
    [setInputInfo]
  );

  const renderContent = () => {
    if (inputInfo.message.length === 0) {
      return (
        <div className="pt-[120px] flex flex-col items-center">
          <Slogan />
          <div className="w-640 rounded-xl shadow-[0_18px_39px_0_rgba(198,202,240,0.1)]">
            <GeneralInput
              placeholder={currentProduct.placeholder}
              showBtn={true}
              disabled={false}
              size="large"
              product={currentProduct}
              send={changeInputInfo}
            />
          </div>
        </div>
      );
    }

    return (
      <ChatView
        inputInfoProp={inputInfo}
        onInputChange={changeInputInfo}
        onProductChange={setProduct}
        productList={productList}
        product={currentProduct}
      />
    );
  };

  return <div className="h-full overflow-hidden">{renderContent()}</div>;
});

HomeWithJotai.displayName = 'HomeWithJotai';

export default HomeWithJotai;

/**
 * <PERSON><PERSON> 简单使用示例
 * 
 * 直接使用 Jotai 的原生 hooks，无需复杂的自定义封装
 */

import React from 'react';
import { useAtom, useAtomValue, useSetAtom } from 'jotai';
import {
  inputInfoAtom,
  currentProductAtom,
  showActionPanelAtom,
  themeAtom,
  hasMessagesAtom,
  isInputValidAtom,
  taskProgressAtom,
} from '@/store';

// ==================== 基础用法示例 ====================

const BasicUsageExample: React.FC = () => {
  // 读写状态
  const [inputInfo, setInputInfo] = useAtom(inputInfoAtom);
  
  // 只读状态
  const theme = useAtomValue(themeAtom);
  
  // 只写状态
  const setShowActionPanel = useSetAtom(showActionPanelAtom);
  
  return (
    <div>
      <h3>当前主题: {theme}</h3>
      <input
        value={inputInfo.message}
        onChange={(e) => setInputInfo({
          ...inputInfo,
          message: e.target.value
        })}
        placeholder="输入消息..."
      />
      <button onClick={() => setShowActionPanel(true)}>
        显示操作面板
      </button>
    </div>
  );
};

// ==================== 多状态管理示例 ====================

const MultiStateExample: React.FC = () => {
  const [inputInfo, setInputInfo] = useAtom(inputInfoAtom);
  const [currentProduct, setCurrentProduct] = useAtom(currentProductAtom);
  const [showActionPanel, setShowActionPanel] = useAtom(showActionPanelAtom);
  const [theme, setTheme] = useAtom(themeAtom);
  
  return (
    <div>
      <h3>多状态管理</h3>
      
      {/* 输入管理 */}
      <div>
        <h4>输入信息</h4>
        <textarea
          value={inputInfo.message}
          onChange={(e) => setInputInfo({
            ...inputInfo,
            message: e.target.value
          })}
          placeholder="输入你的问题..."
        />
        
        <label>
          <input
            type="checkbox"
            checked={inputInfo.deepThink}
            onChange={(e) => setInputInfo({
              ...inputInfo,
              deepThink: e.target.checked
            })}
          />
          深度思考
        </label>
      </div>
      
      {/* 操作按钮 */}
      <div>
        <button onClick={() => setInputInfo({ message: '', deepThink: false })}>
          重置输入
        </button>
        <button onClick={() => setShowActionPanel(!showActionPanel)}>
          {showActionPanel ? '隐藏' : '显示'}操作面板
        </button>
      </div>
      
      {/* 主题切换 */}
      <div>
        <h4>主题设置</h4>
        <select
          value={theme}
          onChange={(e) => setTheme(e.target.value as 'light' | 'dark')}
        >
          <option value="light">浅色主题</option>
          <option value="dark">深色主题</option>
        </select>
      </div>
      
      {/* 产品选择 */}
      <div>
        <h4>当前产品</h4>
        {currentProduct ? (
          <div>
            <span>{currentProduct.name}</span>
            <button onClick={() => setCurrentProduct(undefined)}>
              清除选择
            </button>
          </div>
        ) : (
          <span>未选择产品</span>
        )}
      </div>
    </div>
  );
};

// ==================== 派生状态示例 ====================

const DerivedStateExample: React.FC = () => {
  // 使用派生状态（只读）
  const hasMessages = useAtomValue(hasMessagesAtom);
  const isInputValid = useAtomValue(isInputValidAtom);
  const taskProgress = useAtomValue(taskProgressAtom);
  
  return (
    <div>
      <h3>派生状态示例</h3>
      
      <div>
        <p>是否有消息: {hasMessages ? '是' : '否'}</p>
        <p>输入是否有效: {isInputValid ? '是' : '否'}</p>
      </div>
      
      <div>
        <h4>任务进度</h4>
        <p>总任务数: {taskProgress.total}</p>
        <p>已完成: {taskProgress.completed}</p>
        <p>进度: {taskProgress.progress}%</p>
        <div style={{ 
          width: '200px', 
          height: '20px', 
          backgroundColor: '#f0f0f0',
          borderRadius: '10px'
        }}>
          <div style={{
            width: `${taskProgress.progress}%`,
            height: '100%',
            backgroundColor: '#4040ff',
            borderRadius: '10px',
            transition: 'width 0.3s ease'
          }} />
        </div>
      </div>
    </div>
  );
};

// ==================== 完整示例组件 ====================

const JotaiUsageExample: React.FC = () => {
  return (
    <div style={{ padding: '20px', maxWidth: '800px' }}>
      <h1>Jotai 简单使用示例</h1>
      
      <section style={{ marginBottom: '40px' }}>
        <h2>基础用法</h2>
        <BasicUsageExample />
      </section>
      
      <section style={{ marginBottom: '40px' }}>
        <h2>多状态管理</h2>
        <MultiStateExample />
      </section>
      
      <section style={{ marginBottom: '40px' }}>
        <h2>派生状态</h2>
        <DerivedStateExample />
      </section>
    </div>
  );
};

export default JotaiUsageExample;

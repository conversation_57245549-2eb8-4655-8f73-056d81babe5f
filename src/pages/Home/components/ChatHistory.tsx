import { memo, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { Conversations, type ConversationsProps } from "@ant-design/x";
import { type GetProp, Popover, Space, Button } from "antd";
import {
  DeleteOutlined,
  EditOutlined,
  UserOutlined,
  SwapRightOutlined,
} from "@ant-design/icons";
import { useSetAtom, useAtom } from "jotai";
import { RESET } from "jotai/utils";
import { tokenAtom } from "@/store";

import { useRequest } from "ahooks";
import { agentApi } from "@/services/agent";

import icon_logo from "@/assets/icon/logo.svg";
import icon_menu from "@/assets/icon/menu.svg";
import icon_plus from "@/assets/icon/plus.svg";
import icon_user from "@/assets/icon/user.svg";

const conversationItems: GetProp<ConversationsProps, "items"> = Array.from({
  length: 6,
}).map((_, index) => {
  return {
    key: `item${index + 1}`,
    label: `Conversation${index}`,
    group: "TASK LIST",
  };
});

const groupable: GetProp<typeof Conversations, "groupable"> = {
  sort(a, b) {
    if (a === b) return 0;

    return a === "Today" ? -1 : 1;
  },
  title: (group, { components: { GroupTitle } }) =>
    group ? (
      <GroupTitle>
        <Space>
          <span>{group}</span>
        </Space>
      </GroupTitle>
    ) : (
      <GroupTitle />
    ),
};

const menuConfig: ConversationsProps["menu"] = conversation => ({
  items: [
    {
      label: "Rename",
      key: "rename",
      icon: <EditOutlined />,
    },
    {
      label: "Delete",
      key: "delete",
      icon: <DeleteOutlined />,
      danger: true,
    },
  ],
  onClick: menuInfo => {
    menuInfo.domEvent.stopPropagation();
    // message.info(`Click ${conversation.key} - ${menuInfo.key}`);
  },
});

const UserPanel = ({ userInfo }: { userInfo: any }) => {
  const setToken = useSetAtom(tokenAtom);

  return (
    <ul className="px-[10px]">
      <li className="py-[6px] border-b-[1px]">
        <UserOutlined />
        <span className="ml-[10px]">{userInfo?.user?.email}</span>
      </li>
      <li className="py-[6px]">
        <SwapRightOutlined style={{ color: "#ff4d4f" }} />
        <span
          className="ml-[10px] text-[#ff4d4f] cursor-pointer"
          onClick={() => {
            setToken(RESET);
          }}
        >
          Sign out
        </span>
      </li>
    </ul>
  );
};

const ChatHistory = memo(({ collapsed, onToggle }: CHAT.ChatHistoryProps) => {
  const navigate = useNavigate();
  const [token] = useAtom(tokenAtom);

  const { data: userInfo } = useRequest(
    async () => {
      if (!token) return;
      const res: any = await agentApi.getUser();
      return res;
    },
    {
      refreshDeps: [token],
    }
  );
  console.log("user", userInfo);

  return (
    <div
      className="py-[16px] flex flex-col transition-all duration-300"
      style={{ background: "transparent", width: collapsed ? "0" : "300px" }}
    >
      <div className="flex justify-between px-[16px]">
        {!collapsed && <img src={icon_logo} />}
        <img
          className="cursor-pointer"
          src={icon_menu}
          onClick={onToggle}
        ></img>
      </div>
      {!collapsed && (
        <>
          <Button className="flex justify-center items-center mx-auto w-[236px] h-[46px] rounded-[999px] bg-[#D0FF35] border-[#D0FF35] text-[#000] text-[14px] font-[400] mt-[16px] mb-[6px]">
            <img src={icon_plus}></img>
            <span>New Chat</span>
          </Button>
          <div className="px-[4px] flex-1">
            <Conversations
              items={conversationItems}
              menu={menuConfig}
              groupable={groupable}
              style={{ width: 292, height: "100%", color: "#fff" }}
            />
          </div>

          {userInfo?.user?.email ? (
            <Popover
              placement="topRight"
              title={""}
              content={<UserPanel userInfo={userInfo} />}
            >
              <Button className="flex justify-center items-center mx-auto w-[236px] h-[46px] border-[#D0FF35] bg-[transparent] text-[#D0FF35] rounded-[16px]">
                <span className="inline-flex items-center">
                  <img src={icon_user} />
                </span>
                <span>{userInfo?.user?.email}</span>
              </Button>
            </Popover>
          ) : (
            <Button
              className="flex justify-center items-center mx-auto w-[236px] h-[46px] border-[#D0FF35] bg-[transparent] text-[#D0FF35] rounded-[16px]"
              onClick={() => {
                navigate("/login");
              }}
            >
              sign in
            </Button>
          )}
        </>
      )}
    </div>
  );
});

export default ChatHistory;

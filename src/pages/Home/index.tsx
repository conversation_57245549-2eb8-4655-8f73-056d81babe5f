import { useState, useCallback, memo } from 'react';
import ChatHistory from './components/ChatHistory';
import GeneralInput from '@/components/GeneralInput';
import Slogan from '@/components/Slogan';
import ChatView from '@/components/ChatView';
import { productList, defaultProduct } from '@/utils/constants';

import img_bg from '@/assets/images/login/bg.png';

type HomeProps = Record<string, never>;

const Home: GenieType.FC<HomeProps> = memo(() => {
  const [inputInfo, setInputInfo] = useState<CHAT.TInputInfo>({
    message: '',
    deepThink: false,
  });
  const [product, setProduct] = useState(defaultProduct);
  const [collapsed, setCollapsed] = useState(false);

  const changeInputInfo = useCallback((info: CHAT.TInputInfo) => {
    setInputInfo(info);
  }, []);

  const renderContent = () => {
    if (inputInfo.message.length === 0) {
      return (
        <div className="pt-[120px] flex flex-col items-center">
          <Slogan />
          <div className="w-640 rounded-xl shadow-[0_18px_39px_0_rgba(198,202,240,0.1)]">
            <GeneralInput
              placeholder={product.placeholder}
              showBtn={true}
              size="big"
              disabled={false}
              product={product}
              send={changeInputInfo}
            />
          </div>
          <div className="w-640 flex flex-wrap gap-16 mt-[16px]">
            {productList.map((item, i) => (
              <div
                key={i}
                className={`w-[22%] h-[36px] cursor-pointer flex items-center justify-center border rounded-[8px] ${item.type === product.type ? 'border-[#4040ff] bg-[rgba(64,64,255,0.02)] text-[#4040ff]' : 'border-[#E9E9F0] text-[#666]'}`}
                onClick={() => setProduct(item)}
              >
                <i className={`font_family ${item.img} ${item.color}`}></i>
                <div className="ml-[6px]">{item.name}</div>
              </div>
            ))}
          </div>
        </div>
      );
    }
    return <ChatView inputInfo={inputInfo} product={product} />;
  };

  return (
    <div
      className="w-full h-full flex"
      style={{
        background: `url(${img_bg}) center/cover`,
      }}
    >
      <ChatHistory
        collapsed={collapsed}
        onToggle={() => setCollapsed(!collapsed)}
      />
      <div
        className="flex-1 py-[15px] pr-[15px] h-full"
        style={{ paddingLeft: collapsed ? '15px' : '0' }}
      >
        <div
          className="h-full flex flex-col items-center rounded-[10px]"
          style={{
            background: `rgba(20, 20, 20, 0.80)`,
          }}
        >
          {renderContent()}
        </div>
      </div>
    </div>
  );
});

Home.displayName = 'Home';

export default Home;

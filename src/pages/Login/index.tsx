import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { Form, Input, Button, Typography } from "antd";
import { useSetAtom } from "jotai";
import { agent<PERSON>pi } from "@/services/agent";
import { token<PERSON>tom } from "@/store";

import img_bg from "@/assets/images/login/bg.png";

const { Title } = Typography;

const Login: React.FC = () => {
  const [form] = Form.useForm();
  const navigate = useNavigate();
  const [isSend, setIsSend] = useState(false);
  const [value, setValue] = useState("");
  const [email, setEmail] = useState("");
  const [countdown, setCountdown] = useState(60);
  const [loading, setLoading] = useState(false);
  const setToken = useSetAtom(tokenAtom);

  // 倒计时
  useEffect(() => {
    let timer: NodeJS.Timeout;
    if (countdown > 0) {
      timer = setTimeout(() => setCountdown(countdown - 1), 1000);
    }
    return () => clearTimeout(timer);
  }, [countdown]);

  const handleFinish = async (values: { email: string }) => {
    console.log("Email submitted:", values.email);
    try {
      setLoading(true);
      await agentApi.sendEmailCode(values.email);
      setCountdown(60);
      setIsSend(true);
      setLoading(false);
    } catch (error) {
      setLoading(false);
      console.error(error);
    }
  };

  const handleReSend = async () => {
    try {
      await agentApi.sendEmailCode(email);
      setCountdown(60);
    } catch (error) {}
  };

  const handleSubmit = async (val: string) => {
    try {
      setLoading(true);
      const res: any = await agentApi.loginEmail({
        email: email,
        code: val,
      });
      if (res.session.access_token) {
        setToken(`Bearer ${res.session.access_token}`);
        navigate("/", { replace: true });
      }
      setLoading(false);
    } catch (error) {
      setLoading(false);
    }
  };

  return (
    <div
      className="w-full h-full flex flex-col justify-center items-center"
      style={{
        background: `url(${img_bg}) center/cover`,
      }}
    >
      <div className="w-[383px]">
        <Title
          level={3}
          className="!text-white text-center mb-20 text-[32px] font-[700]"
        >
          {isSend ? "Verify your email" : "Sign in with your email"}
        </Title>
        {!isSend ? (
          <Form form={form} onFinish={handleFinish} layout="vertical">
            <Form.Item
              label={<span className="text-white">Email address</span>}
              name="email"
              rules={[
                { required: true, message: "Please input your email!" },
                // { type: "email", message: "Invalid email format!" },
                {
                  validator: (_, value) => {
                    if (!value) return Promise.resolve();
                    const allowedDomains = ["@wujieai.com", "@maze.guru"];
                    const isValid = allowedDomains.some((domain) =>
                      value.toLowerCase().endsWith(domain)
                    );
                    return isValid
                      ? Promise.resolve()
                      : Promise.reject(
                          new Error(
                            "Only @wujieai.com or @maze.guru emails are allowed!"
                          )
                        );
                  },
                },
              ]}
            >
              <Input
                autoComplete="off"
                placeholder="Only supports @wujieai.com email addresses"
                className="rounded-[10px] h-50 placeholder:text-gray-400 bg-[transparent] border-[#D9D9D9]"
                onChange={(e) => setEmail(e.target.value)}
              />
            </Form.Item>

            <Form.Item>
              <Button
                loading={loading}
                type="primary"
                htmlType="submit"
                className="w-full h-50 text-[16px]  font-[400] rounded-[10px] bg-[#D0FF35] text-black border-none"
              >
                Continue
              </Button>
            </Form.Item>
          </Form>
        ) : (
          <div className="flex flex-col justify-center items-center">
            <p className="text-center text-white text-[14px] font-[400] mb-[20px]">
              We've emailed a one time security code to {email},please enter it
              below:
            </p>

            <Input.OTP
              className="input-otp"
              value={value}
              onKeyDown={(e) => {
                if (
                  !/[0-9]/.test(e.key) &&
                  e.key !== "Backspace" &&
                  e.key !== "Tab" &&
                  e.key !== "ArrowLeft" &&
                  e.key !== "ArrowRight"
                ) {
                  e.preventDefault();
                }
              }}
              onChange={(val) => {
                const numericVal = val.replace(/\D/g, "");
                setValue(numericVal);
                handleSubmit(numericVal);
              }}
              onPaste={(e) => {
                e.preventDefault();
                const paste = e.clipboardData
                  .getData("text")
                  .replace(/\D/g, "");
                setValue(paste);
              }}
              length={6}
              size="large"
              formatter={(str) => str.toUpperCase()}
              style={{
                marginBottom: 20,
              }}
            />

            <Button
              loading={loading}
              type="primary"
              htmlType="submit"
              className="w-full h-50 text-[16px]  font-[400] rounded-[10px] bg-[#D0FF35] text-black border-none"
              onClick={() => handleSubmit(value)}
            >
              Continue
            </Button>
            {countdown === 0 ? (
              <p
                className="text-[#D0FF35] mt-10 text-[14px] font-[400] cursor-pointer"
                onClick={handleReSend}
              >
                Resend Code
              </p>
            ) : (
              <p className="text-[#D0FF35] mt-10 text-[14px] font-[400]">
                Resend in {countdown}s
              </p>
            )}
          </div>
        )}
      </div>
      <p className="absolute left-0 right-0 bottom-[30px] mx-auto text-gray-400 text-center">
        By continuing, you agree to Terms of Service and Privacy Policy.
      </p>
    </div>
  );
};

export default Login;

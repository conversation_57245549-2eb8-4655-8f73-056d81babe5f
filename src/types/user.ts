declare global {
  // eslint-disable-next-line @typescript-eslint/no-namespace
  namespace USER {
    interface Info {
      avatar: string;
      created_at: string;
      id: number;
      name: string;
      uid: string;
      updated_at: string;
    }
    interface User {
      action_link?: string | null;
      app_metadata: any;
      aud: string;
      confirmation_sent_at: string;
      confirmed_at: string;
      created_at: string;
      email: string;
      email_change_sent_at: string | null;
      email_confirmed_at: string;
      factors: any;
      id: string;
      identities: any;
      invited_at: string | null;
      is_anonymous: boolean;
      last_sign_in_at: string;
      new_email: string | null;
      new_phone: string | null;
      phone: string;
      phone_confirmed_at: string | null;
      recovery_sent_at: string;
      role: string;
      updated_at: string;
      user_metadata: any;
    }

    export interface UserInfo {
      info: Info;
      user: User;
    }
  }
}

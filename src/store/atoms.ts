import { atom } from "jotai";
import { atomWithStorage } from "jotai/utils";

// ==================== 基础状态原子 ====================

/**
 * 用户输入信息
 */
export const inputInfoAtom = atom<CHAT.TInputInfo>({
  message: "",
  deepThink: false,
});

/**
 * 当前选择的产品
 */
export const currentProductAtom = atom<CHAT.Product | undefined>(undefined);

/**
 * 聊天项目列表
 */
export const chatItemsAtom = atom<CHAT.ChatItem[]>([]);

/**
 * 当前活跃任务
 */
export const activeTaskAtom = atom<CHAT.Task | undefined>(undefined);

/**
 * 任务列表
 */
export const taskListAtom = atom<CHAT.Task[]>([]);

/**
 * 计划信息
 */
export const planAtom = atom<CHAT.Plan | undefined>(undefined);

/**
 * 是否显示操作面板
 */
export const showActionPanelAtom = atom<boolean>(false);

/**
 * 当前预览文件
 */
export const previewFileAtom = atom<CHAT.TFile | undefined>(undefined);

/**
 * 加载状态
 */
export const loadingAtom = atom<boolean>(false);

// ==================== 持久化状态 ====================

/**
 * 主题设置 (自动保存到 localStorage)
 */
export const themeAtom = atomWithStorage<"light" | "dark">("theme", "light");

/**
 * 语言设置 (自动保存到 localStorage)
 */
export const languageAtom = atomWithStorage<"zh-CN" | "en-US">(
  "language",
  "zh-CN"
);

/**
 * 用户偏好设置 (自动保存到 localStorage)
 */
export const userPreferencesAtom = atomWithStorage("userPreferences", {
  autoScroll: true,
  showTimestamp: true,
  soundEnabled: false,
});

// token
const stringStorage = {
  getItem: (key: string) => localStorage.getItem(key) ?? "",
  setItem: (key: string, value: string) => localStorage.setItem(key, value),
  removeItem: (key: string) => localStorage.removeItem(key),
};

export const tokenAtom = atomWithStorage("token", "", stringStorage);

// ==================== 派生状态（只读计算属性） ====================

/**
 * 是否有聊天项目
 */
export const hasMessagesAtom = atom((get) => {
  const chatItems = get(chatItemsAtom);
  return chatItems.length > 0;
});

/**
 * 输入是否有效
 */
export const isInputValidAtom = atom((get) => {
  const inputInfo = get(inputInfoAtom);
  return inputInfo.message.trim().length > 0;
});

/**
 * 任务进度统计
 */
export const taskProgressAtom = atom((get) => {
  const plan = get(planAtom);
  if (!plan?.stepStatus) return { total: 0, completed: 0, progress: 0 };

  const total = plan.stepStatus.length;
  const completed = plan.stepStatus.filter(
    (status) => status === "completed"
  ).length;
  const progress = total > 0 ? Math.round((completed / total) * 100) : 0;

  return { total, completed, progress };
});

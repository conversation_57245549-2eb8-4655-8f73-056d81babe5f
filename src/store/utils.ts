import { atomWithStorage } from "jotai/utils";

/**
 * 创建带有默认值和验证的持久化原子
 */
export function createPersistentAtom<T>(
  key: string,
  initialValue: T,
  validator?: (value: unknown) => value is T
) {
  return atomWithStorage<T>(key, initialValue, {
    getItem: (key: string, initialValue: T) => {
      try {
        const item = localStorage.getItem(key);
        if (item === null) return initialValue;

        const parsed = JSON.parse(item);
        if (validator && !validator(parsed)) {
          console.warn(`Invalid stored value for ${key}, using default`);
          return initialValue;
        }

        return parsed;
      } catch (error) {
        console.error(`Error reading ${key} from localStorage:`, error);
        return initialValue;
      }
    },
    setItem: (key: string, value: T) => {
      try {
        localStorage.setItem(key, JSON.stringify(value));
      } catch (error) {
        console.error(`<PERSON>rror writing ${key} to localStorage:`, error);
      }
    },
    removeItem: (key: string) => {
      try {
        localStorage.removeItem(key);
      } catch (error) {
        console.error(`Error removing ${key} from localStorage:`, error);
      }
    },
  });
}

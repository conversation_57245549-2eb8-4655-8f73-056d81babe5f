# Jotai 状态管理 - 简化版

本项目已集成 Jotai 作为状态管理解决方案。采用最简单直接的使用方式，无复杂封装。

## 📦 已安装的包

- `jotai` - 核心状态管理库
- `jotai/utils` - 实用工具包（用于持久化等功能）

## 🗂 文件结构

```
src/store/
├── index.ts          # 入口文件，导出所有内容
├── atoms.ts          # 原子状态定义
├── providers.tsx     # Jotai Provider 组件
├── utils.ts          # 工具函数（可选）
└── store.ts          # Store 实例配置（可选）
```

## 🚀 使用方法

### 1. 基础用法

直接使用 Jotai 的原生 hooks：

```typescript
import { useAtom, useAtomValue, useSetAtom } from 'jotai';
import { inputInfoAtom, themeAtom, showActionPanelAtom } from '@/store';

function MyComponent() {
  // 读写状态
  const [inputInfo, setInputInfo] = useAtom(inputInfoAtom);
  
  // 只读状态
  const theme = useAtomValue(themeAtom);
  
  // 只写状态
  const setShowActionPanel = useSetAtom(showActionPanelAtom);
  
  return (
    <div>
      <input 
        value={inputInfo.message}
        onChange={(e) => setInputInfo({
          ...inputInfo, 
          message: e.target.value
        })}
      />
      <p>当前主题: {theme}</p>
      <button onClick={() => setShowActionPanel(true)}>
        显示面板
      </button>
    </div>
  );
}
```

### 2. 派生状态（只读）

```typescript
import { useAtomValue } from 'jotai';
import { hasMessagesAtom, isInputValidAtom, taskProgressAtom } from '@/store';

function StatusComponent() {
  const hasMessages = useAtomValue(hasMessagesAtom);
  const isInputValid = useAtomValue(isInputValidAtom);
  const progress = useAtomValue(taskProgressAtom);
  
  return (
    <div>
      <p>有消息: {hasMessages ? '是' : '否'}</p>
      <p>输入有效: {isInputValid ? '是' : '否'}</p>
      <p>任务进度: {progress.progress}%</p>
    </div>
  );
}
```

### 3. 多状态管理

```typescript
import { useAtom } from 'jotai';
import { 
  inputInfoAtom, 
  currentProductAtom, 
  showActionPanelAtom,
  themeAtom 
} from '@/store';

function MultiStateComponent() {
  const [inputInfo, setInputInfo] = useAtom(inputInfoAtom);
  const [currentProduct, setCurrentProduct] = useAtom(currentProductAtom);
  const [showActionPanel, setShowActionPanel] = useAtom(showActionPanelAtom);
  const [theme, setTheme] = useAtom(themeAtom);
  
  const resetInput = () => {
    setInputInfo({ message: '', deepThink: false });
  };
  
  const togglePanel = () => {
    setShowActionPanel(!showActionPanel);
  };
  
  return (
    <div>
      {/* 你的组件内容 */}
    </div>
  );
}
```

## 📋 可用的状态

### 基础状态
- `inputInfoAtom` - 用户输入信息
- `currentProductAtom` - 当前选择的产品
- `chatItemsAtom` - 聊天项目列表
- `activeTaskAtom` - 当前活跃任务
- `taskListAtom` - 任务列表
- `planAtom` - 计划信息
- `showActionPanelAtom` - 是否显示操作面板
- `previewFileAtom` - 当前预览文件
- `loadingAtom` - 加载状态

### 持久化状态（自动保存到 localStorage）
- `themeAtom` - 主题设置
- `languageAtom` - 语言设置
- `userPreferencesAtom` - 用户偏好设置

### 派生状态（只读计算属性）
- `hasMessagesAtom` - 是否有消息
- `isInputValidAtom` - 输入是否有效
- `taskProgressAtom` - 任务进度统计

## 🎯 核心优势

1. **简单直接** - 无需复杂的封装，直接使用 Jotai 原生 API
2. **原子化** - 状态细粒度管理，只有相关组件重新渲染
3. **类型安全** - 完整的 TypeScript 支持
4. **自动持久化** - 某些状态自动保存到 localStorage
5. **派生状态** - 基于其他状态自动计算的只读属性

## � Provider 配置

确保在应用根部包裹 StoreProvider：

```typescript
import { StoreProvider } from '@/store';

function App() {
  return (
    <StoreProvider>
      <YourApp />
    </StoreProvider>
  );
}
```

## 📚 参考资源

- [Jotai 官方文档](https://jotai.org/)
- [使用示例](../examples/JotaiUsageExample.tsx)

这就是全部！无需复杂的配置或学习成本，直接开始使用即可。

import React from 'react';
import { ConfigProvider } from 'antd';
import { RouterProvider } from 'react-router-dom';
import zhCN from 'antd/locale/zh_CN';
import router from './router';
import StoreProvider from './store/providers';
import { themeConfig } from './constants/theme';

// App 组件：应用的根组件，设置全局配置和路由
const App: GenieType.FC = React.memo(() => {
  return (
    <StoreProvider>
      <ConfigProvider locale={zhCN} theme={themeConfig}>
        <RouterProvider router={router} />
      </ConfigProvider>
    </StoreProvider>
  );
});

export default App;

export const themeConfig = {
  token: {
    // Seed Token，影响范围大
    colorPrimary: "#D0FF35",
    // colorBgBase: "",
    colorTextBase: "#fff",
  },
  components: {
    Input: {
      colorTextBase: "#fff",
    },
    Button: {
      colorPrimary: "#D0FF35",
    },
    Dropdown: {
      colorBgElevated: "#1e1e1e", // 下拉菜单背景色
      colorText: "#fff", // 下拉文字颜色
    },
    Menu: {
      colorBgContainer: "#1e1e1e", // 菜单背景色
      colorItemBg: "#1e1e1e", // item 背景色
      colorText: "#fff", // 菜单文字色
      colorTextDisabled: "#888", // 禁用文字色
      colorItemTextHover: "#fff", // hover 时文字色
      colorItemBgHover: "#333", // hover 背景色
    },
    Popover: {
      colorBgElevated: "#1e1e1e", // Popover 背景
      colorText: "#fff", // Popover 文字颜色
      colorTextHeading: "#fff", // 标题颜色
      colorTextDescription: "#ccc", // 描述文字
    },
  },
}
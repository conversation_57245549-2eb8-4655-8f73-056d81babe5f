import { API_HOST } from './constants';

const isDev = process.env.NODE_ENV === 'development';

const BASE_URL = isDev ? '' : API_HOST;
const API_VERSION = 'v2';

const _api = {
  // user
  send_email_code: `${BASE_URL}/user-manager/v1/user/send-email-code`,
  login_email: `${BASE_URL}/user-manager/v1/user/login-email`,
  user_info: `${BASE_URL}/user-manager/v1/user/get`,

  // app
  agents: `${BASE_URL}/${API_VERSION}/agents`,
  models: `${BASE_URL}/${API_VERSION}/models`,
  agent_detail: `${BASE_URL}/${API_VERSION}/agents/:id`,

  // sse
  chat: `${BASE_URL}/${API_VERSION}/agents/web_agent/runs`,
  chat_team: `${BASE_URL}/${API_VERSION}/teams/route_team/runs`,
};

export default _api;

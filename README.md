# Maze Agent UI

Maze Agent UI 是一个基于 React、TypeScript 和 Vite 的现代化项目。

## 功能特性

- 基于 React 19 和 TypeScript
- 使用 Vite 作为构建工具，提供快速的开发体验
- 集成 Ant Design 组件库
- 支持 Markdown 渲染
- 文件预览和处理功能
- 使用 ESLint 和 Prettier 进行代码规范化

## 快速开始

1. 安装依赖：

```bash
pnpm install
```

2. 启动开发服务器：

```bash
pnpm run dev
```

3. 在浏览器中打开 http://localhost:3000 查看应用。

## 可用脚本

- `pnpm dev`: 启动开发服务器
- `pnpm build`: 构建生产版本
- `pnpm lint`: 运行 ESLint 检查
- `pnpm fix`: 自动修复 ESLint 问题
- `pnpm preview`: 预览生产构建
